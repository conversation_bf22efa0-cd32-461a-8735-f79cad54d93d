# MongoDB Atlas Connection
MONGODB_URI=mongodb+srv://anuragmishra3407:<EMAIL>/haat

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-here
JWT_EXPIRE=7d
JWT_REFRESH_EXPIRE=30d

# Google Maps API
GOOGLE_MAPS_API_KEY=AIzaSyAPREHAXknXIDLKG6hHhpty99gxlOlkpRw
# Gemini AI API
GEMINI_API_KEY=AIzaSyAB9ZoyN9FU9D-5jgCJvORMKqhwF6Onglc

# Server Configuration
NODE_ENV=development
PORT=5000

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Email Configuration (Optional - for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password
