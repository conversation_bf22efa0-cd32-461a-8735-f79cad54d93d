{"name": "hatt", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/react": "^3.23.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/inter": "^5.2.6", "@fontsource/poppins": "^5.2.6", "@google/maps": "^1.1.3", "@googlemaps/js-api-loader": "^1.16.10", "@tanstack/react-query": "^5.83.0", "axios": "^1.11.0", "framer-motion": "^12.23.9", "jwt-decode": "^4.0.0", "lucide-react": "^0.526.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.7.1", "react-toastify": "^11.0.5", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}