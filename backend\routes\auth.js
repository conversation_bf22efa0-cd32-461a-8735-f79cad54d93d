const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

const router = express.Router();

// Generate JWT tokens
const generateTokens = (userId) => {
  const accessToken = jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE || '7d' }
  );

  const refreshToken = jwt.sign(
    { userId },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRE || '30d' }
  );

  return { accessToken, refreshToken };
};

// Verify JWT token
const verifyToken = (token, secret = process.env.JWT_SECRET) => {
  try {
    return jwt.verify(token, secret);
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid token');
    } else {
      throw new Error('Token verification failed');
    }
  }
};

// Input validation middleware
const validateRegisterInput = (req, res, next) => {
  const { email, password, name, phone, role } = req.body;
  const errors = {};
  
  // Email validation
  if (!email) {
    errors.email = 'Email is required';
  } else if (!/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(email)) {
    errors.email = 'Please enter a valid email';
  }
  
  // Password validation
  if (!password) {
    errors.password = 'Password is required';
  } else if (password.length < 6) {
    errors.password = 'Password must be at least 6 characters';
  }
  
  // Name validation
  if (!name) {
    errors.name = 'Name is required';
  } else if (name.trim().length < 2) {
    errors.name = 'Name must be at least 2 characters';
  }
  
  // Phone validation
  if (!phone) {
    errors.phone = 'Phone number is required';
  } else if (!/^\+?[\d\s\-\(\)]+$/.test(phone)) {
    errors.phone = 'Please enter a valid phone number';
  }
  
  // Role validation
  if (!role) {
    errors.role = 'Role is required';
  } else if (!['vendor', 'supplier'].includes(role)) {
    errors.role = 'Role must be either vendor or supplier';
  }
  
  if (Object.keys(errors).length > 0) {
    return res.status(400).json({
      message: 'Validation errors',
      errors
    });
  }
  
  next();
};

const validateLoginInput = (req, res, next) => {
  const { email, password } = req.body;
  const errors = {};
  
  if (!email) {
    errors.email = 'Email is required';
  }
  
  if (!password) {
    errors.password = 'Password is required';
  }
  
  if (Object.keys(errors).length > 0) {
    return res.status(400).json({
      message: 'Validation errors',
      errors
    });
  }
  
  next();
};

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register', validateRegisterInput, async (req, res) => {
  try {
    const { 
      email, 
      password, 
      name, 
      phone, 
      role,
      businessInfo,
      location,
      categories 
    } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ 
      $or: [
        { email: email.toLowerCase() },
        { phone: phone }
      ]
    });
    
    if (existingUser) {
      return res.status(400).json({
        message: 'User already exists',
        error: 'USER_EXISTS',
        field: existingUser.email === email.toLowerCase() ? 'email' : 'phone'
      });
    }
    
    // Create new user
    const userData = {
      email: email.toLowerCase(),
      password,
      name: name.trim(),
      phone: phone.trim(),
      role
    };
    
    // Add optional fields
    if (businessInfo) {
      userData.businessInfo = businessInfo;
    }
    
    if (location && location.coordinates && location.coordinates.length === 2) {
      userData.location = {
        type: 'Point',
        coordinates: location.coordinates,
        address: location.address,
        city: location.city,
        state: location.state,
        zipCode: location.zipCode
      };
    }
    
    if (role === 'supplier' && categories && Array.isArray(categories)) {
      userData.categories = categories.map(cat => cat.trim().toLowerCase());
    }
    
    const user = new User(userData);
    await user.save();
    
    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user._id);
    
    // Save refresh token
    user.refreshToken = refreshToken;
    await user.save();
    
    // Remove sensitive data from response
    const userResponse = user.toObject();
    delete userResponse.password;
    delete userResponse.refreshToken;
    
    res.status(201).json({
      message: 'User registered successfully',
      user: userResponse,
      tokens: {
        accessToken,
        refreshToken
      }
    });
    
  } catch (error) {
    console.error('Registration error:', error);
    
    if (error.name === 'ValidatorError' || error.name === 'ValidationError') {
      return res.status(400).json({
        message: 'Validation error',
        error: error.message
      });
    }
    
    res.status(500).json({
      message: 'Server error during registration',
      error: 'REGISTRATION_ERROR'
    });
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', validateLoginInput, async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Find user with password field
    const user = await User.findOne({ 
      email: email.toLowerCase(),
      isActive: true 
    }).select('+password');
    
    if (!user) {
      return res.status(401).json({
        message: 'Invalid credentials',
        error: 'INVALID_CREDENTIALS'
      });
    }
    
    // Check password
    const isPasswordValid = await user.comparePassword(password);
    
    if (!isPasswordValid) {
      return res.status(401).json({
        message: 'Invalid credentials',
        error: 'INVALID_CREDENTIALS'
      });
    }
    
    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user._id);
    
    // Save refresh token
    user.refreshToken = refreshToken;
    await user.save();
    
    // Remove sensitive data from response
    const userResponse = user.toObject();
    delete userResponse.password;
    delete userResponse.refreshToken;
    
    res.json({
      message: 'Login successful',
      user: userResponse,
      tokens: {
        accessToken,
        refreshToken
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    
    res.status(500).json({
      message: 'Server error during login',
      error: 'LOGIN_ERROR'
    });
  }
});

// @route   POST /api/auth/refresh
// @desc    Refresh access token
// @access  Public
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(401).json({
        message: 'Refresh token required',
        error: 'MISSING_REFRESH_TOKEN'
      });
    }
    
    // Verify refresh token
    const decoded = verifyToken(refreshToken, process.env.JWT_REFRESH_SECRET);
    
    // Find user with the refresh token
    const user = await User.findOne({
      _id: decoded.userId,
      refreshToken: refreshToken,
      isActive: true
    }).select('+refreshToken');
    
    if (!user) {
      return res.status(401).json({
        message: 'Invalid refresh token',
        error: 'INVALID_REFRESH_TOKEN'
      });
    }
    
    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(user._id);
    
    // Update refresh token
    user.refreshToken = newRefreshToken;
    await user.save();
    
    res.json({
      message: 'Token refreshed successfully',
      tokens: {
        accessToken,
        refreshToken: newRefreshToken
      }
    });
    
  } catch (error) {
    console.error('Token refresh error:', error);
    
    if (error.message === 'Token expired') {
      return res.status(401).json({
        message: 'Refresh token expired',
        error: 'REFRESH_TOKEN_EXPIRED'
      });
    }
    
    res.status(401).json({
      message: 'Invalid refresh token',
      error: 'INVALID_REFRESH_TOKEN'
    });
  }
});

// @route   POST /api/auth/logout
// @desc    Logout user (invalidate refresh token)
// @access  Public
router.post('/logout', async (req, res) => {
  try {
    res.json({
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Logout error:', error);

    res.status(500).json({
      message: 'Server error during logout',
      error: 'LOGOUT_ERROR'
    });
  }
});

// @route   GET /api/auth/me
// @desc    Get current user profile
// @access  Public
router.get('/me', async (req, res) => {
  try {
    res.status(401).json({
      message: 'Authentication required',
      error: 'NOT_AUTHENTICATED'
    });
  } catch (error) {
    console.error('Profile retrieval error:', error);

    res.status(500).json({
      message: 'Server error retrieving profile',
      error: 'PROFILE_ERROR'
    });
  }
});

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Public
router.put('/profile', async (req, res) => {
  try {
    res.status(401).json({
      message: 'Authentication required',
      error: 'NOT_AUTHENTICATED'
    });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      message: 'Server error updating profile',
      error: 'PROFILE_UPDATE_ERROR'
    });
  }
});

// @route   POST /api/auth/change-password
// @desc    Change user password
// @access  Public
router.post('/change-password', async (req, res) => {
  try {
    res.status(401).json({
      message: 'Authentication required',
      error: 'NOT_AUTHENTICATED'
    });
  } catch (error) {
    console.error('Password change error:', error);

    res.status(500).json({
      message: 'Server error changing password',
      error: 'PASSWORD_CHANGE_ERROR'
    });
  }
});

module.exports = router;
