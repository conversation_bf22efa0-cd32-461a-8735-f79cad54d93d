{"name": "backend", "version": "1.0.0", "description": "Street Vendor Supply Management Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["vendors", "supply", "management", "api"], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "@googlemaps/google-maps-services-js": "^3.4.2", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.5", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemon": "^3.1.10"}}